import React, { PropsWithChildren, useState } from 'react'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Drawer, DrawerClose, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const [activeTab, setActiveTab] = useState('material-settings')

  const tabs = [
    { value: 'material-settings', label: '混剪素材设置' },
    { value: 'video-dedup', label: '视频智能去重' },
    { value: 'background-music', label: '背景音乐设置' },
    { value: 'subtitle-style', label: '随机字幕样式设置' },
    { value: 'text-group-style', label: '随机文字组样式设置' },
    { value: 'voice-style', label: '随机口播音色设置' },
    { value: 'background-style', label: '随机背景设置' },
    { value: 'effect-style', label: '随机特效设置' },
  ]

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full">
          {tabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="h-full">
              <div className="flex items-center justify-center h-full text-muted-foreground">
                {tab.label} 内容区域
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
export const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <Drawer direction="right">
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>
      </DrawerContent>
    </Drawer>
  )
}
